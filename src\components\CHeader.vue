<!-- 顶部标题 -->
<template>
  <header class="header">市政设施运维管理平台</header>
  <!-- 市政设施统计卡片 -->
  <div class="facility-stats">
    <div class="stat-card">
      <div class="stat-icon">
        <img src="@/assets/images/ic_car.png" alt="车行桥梁" />
      </div>
      <div class="stat-content">
        <div class="stat-label">车行桥梁</div>
        <div class="stat-value">45<span class="stat-unit">个</span></div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">
        <img src="@/assets/images/ic_blind.png" alt="人行天桥" />
      </div>
      <div class="stat-content">
        <div class="stat-label">人行天桥</div>
        <div class="stat-value">13<span class="stat-unit">个</span></div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">
        <img src="@/assets/images/ic_under.png" alt="地下通道" />
      </div>
      <div class="stat-content">
        <div class="stat-label">地下通道</div>
        <div class="stat-value">0<span class="stat-unit">个</span></div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">
        <img src="@/assets/images/ic_tunnel.png" alt="隧道" />
      </div>
      <div class="stat-content">
        <div class="stat-label">隧道</div>
        <div class="stat-value">9<span class="stat-unit">个</span></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 市政设施统计数据
const facilityStats = [
  {
    label: '车行桥梁',
    value: 45,
    unit: '个',
    icon: 'ic_car.png'
  },
  {
    label: '人行天桥',
    value: 13,
    unit: '个',
    icon: 'ic_blind.png'
  },
  {
    label: '地下通道',
    value: 0,
    unit: '个',
    icon: 'ic_under.png'
  },
  {
    label: '隧道',
    value: 9,
    unit: '个',
    icon: 'ic_tunnel.png'
  }
]
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  top: 12px;
  width: calc(100% - 24px);
  height: 87px;
  background: url('@/assets/images/bg_top.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
}

.facility-stats {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.stat-card {
  width: 180px;
  height: 80px;
  background: url('@/assets/images/bg_center_top_box.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  position: relative;

  .stat-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .stat-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 5px;
      line-height: 1;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #00FFFF;
      line-height: 1;

      .stat-unit {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-left: 2px;
      }
    }
  }
}
</style>
